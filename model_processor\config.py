"""
配置文件 - 包含映射规则和系统配置
"""

import logging

# 日志配置
LOG_LEVEL = logging.INFO
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# 图标相关配置
ICON_BASE_PATH = "lobe-icons/packages/static-png/light"
ICON_BASE_URL = "https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light"

# 厂商名称映射 - 将模型名称关键词映射到对应的图标文件名
VENDOR_MAPPING = {
    # OpenAI系列
    'gpt': 'openai',
    'openai': 'openai',
    'dall-e': 'dalle',
    'dalle': 'dalle',
    'o1': 'openai',
    'o3': 'openai',  # o3-mini 等模型
    'text-embedding': 'openai',  # text-embedding-3-small 等嵌入模型
    'omni-moderation': 'openai',  # omni-moderation-latest 等审核模型
    'tts': 'openai',  # tts-1 等语音合成模型
    
    # Anthropic系列
    'claude': 'claude',
    'anthropic': 'anthropic',
    
    # Google系列
    'gemini': 'gemini',
    'palm': 'palm',
    'bard': 'gemini',
    'google': 'google',
    'vertex': 'vertexai',
    'imagen': 'gemini',
    'chat-bison': 'palm',  # chat-bison-001 等 PaLM 模型
    'text-bison': 'palm',  # text-bison-001 等 PaLM 模型
    'bison': 'palm',  # 通用 bison 系列模型
    'veo': 'gemini',  # Veo 视频生成模型
    'gemini-2.5': 'gemini',  # Gemini 2.5 系列
    'gemini-2.0': 'gemini',  # Gemini 2.0 系列
    'gemini-1.5': 'gemini',  # Gemini 1.5 系列
    'gemini-embedding': 'gemini',  # Gemini 嵌入模型
    
    # 阿里系列
    'qwen': 'qwen',
    'qvq': 'qwen',  # QVQ 是阿里的推理模型
    'qwq': 'qwen',  # QWQ 也是阿里的推理模型
    'qwen3': 'qwen',  # Qwen3 第三代系列
    'qwen2.5': 'qwen',  # Qwen2.5 系列
    'qwen2': 'qwen',  # Qwen2 系列
    'qvq-max': 'qwen',  # QVQ-Max 视觉推理模型
    'tongyi': 'qwen',
    'alibaba': 'alibaba',
    'alibabacloud': 'alibabacloud',
    
    # 百度系列
    'wenxin': 'wenxin',
    'baidu': 'baidu',
    'ernie': 'wenxin',
    
    # 腾讯系列
    'hunyuan': 'hunyuan',
    'tencent': 'tencent',
    
    # 字节跳动系列
    'doubao': 'doubao',
    'bytedance': 'bytedance',
    
    # DeepSeek系列
    'deepseek': 'deepseek',
    
    # 智谱系列
    'chatglm': 'chatglm',
    'glm': 'chatglm',
    'zhipu': 'zhipu',
    
    # 月之暗面系列
    'kimi': 'kimi',
    'moonshot': 'moonshot',
    
    # xAI系列
    'grok': 'grok',
    'xai': 'xai',
    
    # Meta系列
    'llama': 'meta',
    'meta': 'meta',
    
    # Mistral系列
    'mistral': 'mistral',
    
    # Cohere系列
    'cohere': 'cohere',
    
    # 其他厂商
    'yi': 'yi',
    'baichuan': 'baichuan',
    'internlm': 'internlm',
    'spark': 'spark',
    'minimax': 'minimax',
    'stepfun': 'stepfun',
    'siliconcloud': 'siliconcloud',
    'microsoft': 'microsoft',
    'azure': 'azure',
}

# 功能关键词映射 - 根据模型名称和描述推断功能标签
FUNCTION_KEYWORDS = {
    '推理模型': ['thinking', 'reasoning', 'r1', 'o1', 'qwq', 'qvq', 'reasoning', '推理', '思维', '思考', '动态思维', '长链式思维'],
    '文生图': ['image', 'generation', 'dall-e', 'dalle', 'midjourney', 'stable', 'flux', 'imagen'],
    '图生图': ['image', 'vision', 'multimodal'],
    '语音合成': ['tts', 'speech', 'voice', '语音生成', 'omni', '自然语音', 'native-audio'],
    '语音识别': ['asr', 'speech', 'whisper'],
    '代码生成': ['code', 'coder', 'copilot', 'programming', '编程', '编码'],
    '搜索': ['search', 'web', 'browse'],
    '信息检索': ['search', 'retrieval', 'rag'],
    '多模态': ['vision', 'multimodal', 'vl', 'omni', '视觉', '图像', '视频', '音频', '全模态'],
    '嵌入模型': ['embedding', 'embed'],
    '视频生成': ['video', 'generation', 'veo', '视频生成'],
    '图像生成': ['image', 'generation', 'imagen', '图像生成'],
    '对话式生成': ['dialog', 'conversation', '对话式'],
    '实时交互': ['live', 'real-time', 'interactive', '实时', '直播'],
    '低延迟': ['low-latency', 'fast', '低延迟', '快速'],
    '成本效益': ['cost-effective', 'lite', '成本效益', '轻量'],
    '高吞吐量': ['high-throughput', '高吞吐量'],
    '自适应思维': ['adaptive', 'thinking', '自适应思维'],
    '增强推理': ['enhanced', 'reasoning', '增强推理'],
    '长上下文': ['long', 'context', '1m', '1000000', '100万', '长上下文', 'turbo'],
    '视觉推理': ['visual', 'reasoning', 'vl', 'vision', '视觉推理', 'qvq'],
    '旗舰模型': ['flagship', 'max', 'plus', '旗舰', '最强', '235b', '72b'],
    '开源模型': ['open', 'source', '开源', 'instruct'],
    '数学推理': ['math', 'mathematical', '数学', '数理'],
    '角色扮演': ['role', 'playing', '角色扮演'],
    '创意写作': ['creative', 'writing', '创意写作', '创作生成'],
    '文本理解': ['understanding', 'comprehension', '理解', '阅读理解'],
    '翻译': ['translation', 'translate', '翻译'],
    '摘要生成': ['summary', 'summarization', '摘要'],
    '改写': ['rewrite', 'paraphrase', '改写'],
    '指令跟随': ['instruction', 'following', '指令跟随'],
    '混合专家': ['moe', 'mixture', 'expert', '混合专家', 'a3b'],
    '端到端': ['end-to-end', '端到端'],
    '实时处理': ['real-time', 'realtime', '实时'],
    '免费': ['free', 'fovt', '公益'],
    '推荐': ['recommend', 'popular', 'featured'],
}

# 厂商标签映射 - 根据匹配到的图标文件推断厂商标签
VENDOR_TAGS = {
    'openai': ['openai'],
    'claude': ['claude'],
    'anthropic': ['anthropic'],
    'gemini': ['gemini'],
    'google': ['google'],
    'palm': ['google', 'palm'],  # PaLM 模型属于 Google
    'qwen': ['qwen'],
    'alibaba': ['alibaba'],
    'deepseek': ['deepseek'],
    'chatglm': ['chatglm'],
    'zhipu': ['zhipu'],
    'kimi': ['kimi'],
    'moonshot': ['moonshot'],
    'grok': ['grok'],
    'meta': ['meta'],
    'mistral': ['mistral'],
    'cohere': ['cohere'],
    'yi': ['yi'],
    'baichuan': ['baichuan'],
    'microsoft': ['microsoft'],
    'siliconcloud': ['硅基流动'],
}

# 特殊处理规则
SPECIAL_RULES = {
    # 硅基流动的特殊处理
    'siliconcloud': {
        'url_pattern': 'siliconcloud-color.png',
        'tags': ['硅基流动']
    },
    # 当贝的特殊处理
    'dangbei': {
        'tags': ['当贝']
    },
    # FOVT公益的特殊处理
    'fovt': {
        'tags': ['fovt公益', '免费']
    },
    # Qwen3系列特殊处理
    'qwen3-235b': {
        'tags': ['旗舰模型', '推理模型', '动态思维预算', '第三代']
    },
    'qwen3-30b': {
        'tags': ['混合专家', '紧凑高性能', '第三代']
    },
    'qwen3-32b': {
        'tags': ['密集模型', '第三代', '推理模型']
    },
    # Qwen2.5系列特殊处理
    'qwen2.5-max': {
        'tags': ['旗舰模型', '最强大', '复杂推理']
    },
    'qwen2.5-plus': {
        'tags': ['旗舰模型', '复杂任务']
    },
    'qwen2.5-turbo': {
        'tags': ['长上下文', '快速', '100万token']
    },
    'qwen2.5-omni': {
        'tags': ['多模态', '端到端', '实时处理', '全模态']
    },
    'qvq-max': {
        'tags': ['视觉推理', '最大', '长链条思考']
    },
    'qwen2.5-vl': {
        'tags': ['视觉语言', '开源', '多模态']
    },
    'qwen2.5-14b-1m': {
        'tags': ['长上下文', '开源', '100万token']
    },
    'qwen2.5-coder': {
        'tags': ['代码生成', '编程', '旗舰编码']
    },
    'qwen2.5-72b': {
        'tags': ['开源', '最大', '多语言']
    },
    # Google Gemini系列特殊处理
    'gemini-2.5-pro': {
        'tags': ['增强推理', '多模态', '高级编码', '思考推理']
    },
    'gemini-2.5-flash': {
        'tags': ['自适应思维', '成本效益', '快速']
    },
    'gemini-2.5-flash-lite': {
        'tags': ['成本效益', '高吞吐量', '轻量']
    },
    'gemini-2.5-flash-preview-native-audio': {
        'tags': ['对话式生成', '语音合成', '自然语音']
    },
    'gemini-2.5-flash-preview-tts': {
        'tags': ['语音合成', '低延迟', '多声道']
    },
    'gemini-2.5-pro-preview-tts': {
        'tags': ['语音合成', '低延迟', '多声道']
    },
    'gemini-2.0-flash': {
        'tags': ['新一代', '快速', '实时交互']
    },
    'gemini-2.0-flash-preview-image-generation': {
        'tags': ['对话式生成', '图像生成', '图像编辑']
    },
    'gemini-2.0-flash-lite': {
        'tags': ['成本效益', '低延迟']
    },
    'gemini-1.5-flash': {
        'tags': ['快速', '多样化性能', '多模态']
    },
    'gemini-1.5-flash-8b': {
        'tags': ['轻量', '大量任务', '成本效益']
    },
    'gemini-1.5-pro': {
        'tags': ['复杂推理', '高智能', '多模态']
    },
    'gemini-embedding': {
        'tags': ['嵌入模型', '文本相关性']
    },
    'imagen-3.0': {
        'tags': ['图像生成', '最先进', '高质量']
    },
    'veo-2.0': {
        'tags': ['视频生成', '高质量视频']
    },
    'gemini-2.0-flash-live': {
        'tags': ['实时交互', '低延迟', '双向语音', '视频互动']
    }
}
